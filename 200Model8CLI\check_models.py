#!/usr/bin/env python3
"""
Standalone model checker for 200Model8CLI
Fetches actual available models from OpenRouter
"""
import asyncio
import os
import sys
import yaml
import httpx
from pathlib import Path
from rich.console import Console

console = Console()

async def check_available_models(free_only=False, update_switcher=False):
    """Check available models from OpenRouter"""
    try:
        # Get API key
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            # Try to get from config file
            config_path = Path.home() / ".200model8cli" / "config.yaml"
            if config_path.exists():
                with open(config_path, 'r') as f:
                    config_data = yaml.safe_load(f) or {}
                api_key = config_data.get("api", {}).get("openrouter_key")
        
        if not api_key:
            console.print("[red]❌ No API key found. Set with: 200model8cli set-api-key YOUR_KEY[/red]")
            return
        
        console.print("[blue]🔍 Fetching models from OpenRouter...[/blue]")
        
        # Fetch models from OpenRouter
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://openrouter.ai/api/v1/models",
                headers={"Authorization": f"Bearer {api_key}"},
                timeout=30.0
            )
            response.raise_for_status()
            
            models_data = response.json()
            models = models_data.get("data", [])
        
        # Filter models
        if free_only:
            free_models = []
            for model in models:
                pricing = model.get("pricing", {})
                prompt_price = pricing.get("prompt", "unknown")
                if prompt_price == "0" or prompt_price == 0:
                    free_models.append(model)
            models = free_models
        
        # Display models
        console.print(f"[green]✅ Found {len(models)} models[/green]")
        
        if free_only:
            console.print("[blue]🆓 Free Models:[/blue]")
        else:
            console.print("[blue]📋 All Models:[/blue]")
        
        working_models = []
        for i, model in enumerate(models[:50], 1):  # Limit to first 50
            model_id = model.get("id", "unknown")
            name = model.get("name", "Unknown")
            pricing = model.get("pricing", {})
            prompt_price = pricing.get("prompt", "unknown")
            
            if prompt_price == "0" or prompt_price == 0:
                price_info = "[green]FREE[/green]"
                working_models.append(model_id)
            else:
                price_info = f"[yellow]${prompt_price}/1K tokens[/yellow]"
            
            console.print(f"  {i:2d}. {model_id}")
            console.print(f"      Name: {name}")
            console.print(f"      Price: {price_info}")
            console.print()
        
        if len(models) > 50:
            console.print(f"[dim]... and {len(models) - 50} more models[/dim]")
        
        # Update switch_model.py if requested
        if update_switcher and free_only and working_models:
            console.print(f"[blue]🔄 Updating switch_model.py with {len(working_models[:10])} working models...[/blue]")
            update_switch_model_file(working_models[:10])
        
        return working_models
        
    except Exception as e:
        console.print(f"[red]❌ Error fetching models: {e}[/red]")
        return []

def update_switch_model_file(model_ids):
    """Update switch_model.py with new working models"""
    try:
        switch_file = Path("switch_model.py")
        if not switch_file.exists():
            console.print("[red]❌ switch_model.py not found[/red]")
            return
        
        content = switch_file.read_text()
        
        # Create new model list
        new_list = "        working_free_models = [\n"
        for model_id in model_ids:
            new_list += f'            "{model_id}",\n'
        new_list += "        ]"
        
        # Replace the old list
        import re
        pattern = r'working_free_models = \[.*?\]'
        new_content = re.sub(pattern, new_list.strip(), content, flags=re.DOTALL)
        
        switch_file.write_text(new_content)
        console.print("[green]✅ Updated switch_model.py with working models[/green]")
        
    except Exception as e:
        console.print(f"[red]❌ Could not update switch_model.py: {e}[/red]")

async def main():
    """Main function"""
    free_only = "--free-only" in sys.argv or "-f" in sys.argv
    update_switcher = "--update" in sys.argv or "-u" in sys.argv
    
    if "--help" in sys.argv or "-h" in sys.argv:
        console.print("[blue]📋 Model Checker for 200Model8CLI[/blue]")
        console.print("Usage:")
        console.print("  python check_models.py [options]")
        console.print()
        console.print("Options:")
        console.print("  --free-only, -f    Show only free models")
        console.print("  --update, -u       Update switch_model.py with working models")
        console.print("  --help, -h         Show this help")
        return
    
    await check_available_models(free_only, update_switcher)

if __name__ == "__main__":
    asyncio.run(main())
