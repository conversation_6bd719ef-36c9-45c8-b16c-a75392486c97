#!/usr/bin/env python3
"""
Standalone model switcher for 200Model8CLI
This bypasses all configuration validation
"""
import sys
import yaml
from pathlib import Path
from rich.console import Console

console = Console()

def switch_model():
    """Switch to next available free model"""
    try:
        # Curated list of actually working free models (updated from OpenRouter)
        working_free_models = [
            "deepseek/deepseek-r1:free",
            "deepseek/deepseek-chat:free",
            "deepseek/deepseek-chat-v3-0324:free",
            "google/gemini-2.0-flash-exp:free",
            "meta-llama/llama-3.3-70b-instruct:free",
            "qwen/qwq-32b:free",
            "google/gemma-3-27b-it:free",
            "mistralai/mistral-small-3.2-24b-instruct:free",
            "moonshotai/kimi-k2:free",
            "featherless/qwerky-72b:free"
        ]
        
        # Get current model from config file
        config_path = Path.home() / ".200model8cli" / "config.yaml"
        current_model = None
        
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    config_data = yaml.safe_load(f) or {}
                current_model = config_data.get("models", {}).get("default")
            except:
                pass
        
        # Find current model index
        current_index = -1
        if current_model:
            try:
                current_index = working_free_models.index(current_model)
                console.print(f"[blue]Current model: {current_model}[/blue]")
            except ValueError:
                console.print(f"[yellow]⚠️ Current model '{current_model}' not in working list[/yellow]")
        
        # Switch to next model
        next_index = (current_index + 1) % len(working_free_models)
        next_model = working_free_models[next_index]
        
        # Update config file
        config_path.parent.mkdir(exist_ok=True)
        
        if config_path.exists():
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f) or {}
        else:
            config_data = {}
        
        if "models" not in config_data:
            config_data["models"] = {}
        config_data["models"]["default"] = next_model
        
        with open(config_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False)
        
        console.print(f"[green]✅ Switched to: {next_model}[/green]")
        console.print(f"[blue]💡 Try: 200model8cli[/blue]")
        
    except Exception as e:
        console.print(f"[red]❌ Error switching model: {e}[/red]")
        return 1
    
    return 0

def list_models():
    """List available models"""
    working_free_models = [
        "deepseek/deepseek-r1:free",
        "deepseek/deepseek-chat-v3-0324:free", 
        "meta-llama/llama-3.2-3b-instruct:free",
        "microsoft/phi-3-mini-4k-instruct:free",
        "qwen/qwen-2.5-7b-instruct:free"
    ]
    
    console.print("[blue]🔄 Available Free Models:[/blue]")
    for i, model in enumerate(working_free_models, 1):
        console.print(f"  {i}. {model}")

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--list":
        list_models()
    else:
        return switch_model()

if __name__ == "__main__":
    sys.exit(main())
